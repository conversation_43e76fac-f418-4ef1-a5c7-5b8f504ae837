const path = require('path');
const chalk = require('chalk');
const { createUnifiedLogService } = require('../infrastructure/logging');

/**
 * 统一失败记录器 - 使用新的统一日志系统
 * 替代原有的 FailureLogger，使用 Markdown 格式便于阅读
 */
class UnifiedFailureLogger {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      enableDetailedLogging: true,
      enableSummaryReport: true,
      ...options
    };

    // 使用统一日志服务
    this.logService = createUnifiedLogService(projectPath, {
      enableDebugLog: this.options.verbose || false,
      defaultFormat: 'markdown'
    });

    // 内存中的失败记录
    this.failures = [];
    this.successes = [];
    this.skipped = [];
  }

  /**
   * 初始化日志记录器
   */
  async initialize() {
    await this.logService.initialize();

    // 开始迁移会话
    this.session = this.logService.startSession({
      type: 'migration',
      projectPath: this.projectPath,
      startTime: new Date().toISOString()
    });

    if (this.options.enableDetailedLogging) {
      console.log(chalk.blue('📝 统一失败记录器已初始化'));
    }
  }

  /**
   * 记录失败文件
   */
  async logFailure(filePath, error, context = {}) {
    const errorMessage = this._extractErrorMessage(error);

    const failure = {
      file: filePath,
      absolutePath: path.resolve(filePath),
      error: errorMessage,
      stack: error && error.stack ? error.stack : '',
      timestamp: new Date().toISOString(),
      context: {
        fileType: path.extname(filePath),
        fileSize: await this._getFileSize(filePath),
        ...context
      }
    };

    this.failures.push(failure);

    // 使用统一日志服务记录错误
    await this.logService.logMigrationError(failure, {
      taskType: 'file-migration',
      phase: context.phase || 'unknown'
    });

    if (this.options.enableDetailedLogging) {
      console.log(chalk.red(`❌ 记录失败文件: ${failure.file}`));
    }
  }

  /**
   * 记录成功文件
   */
  async logSuccess(filePath, transformInfo = {}, context = {}) {
    const success = {
      file: filePath,
      absolutePath: path.resolve(filePath),
      transformInfo,
      timestamp: new Date().toISOString(),
      context: {
        fileType: path.extname(filePath),
        fileSize: await this._getFileSize(filePath),
        ...context
      }
    };

    this.successes.push(success);

    // 使用统一日志服务记录成功
    // await this.logService.logMigrationSuccess(success, {
    //   taskType: 'file-migration',
    //   phase: context.phase || 'unknown'
    // });

    if (this.options.enableDetailedLogging) {
      console.log(chalk.green(`✅ 记录成功文件: ${success.file}`));
    }
  }

  /**
   * 记录跳过的文件
   */
  async logSkipped(filePath, reason, context = {}) {
    const skipped = {
      file: filePath,
      absolutePath: path.resolve(filePath),
      reason,
      timestamp: new Date().toISOString(),
      context: {
        fileType: path.extname(filePath),
        fileSize: await this._getFileSize(filePath),
        ...context
      }
    };

    this.skipped.push(skipped);

    if (this.options.enableDetailedLogging) {
      console.log(chalk.yellow(`⏭️  跳过文件: ${skipped.file} - ${reason}`));
    }
  }

  /**
   * 生成并保存失败报告
   */
  async saveFailures() {
    if (this.failures.length === 0 && this.successes.length === 0) {
      console.log(chalk.green('🎉 没有需要记录的文件!'));
      return;
    }

    // 生成摘要报告
    const summary = this._generateSummary();

    // 使用统一日志服务生成综合报告
    const { report, filePath } = await this.logService.generateComprehensiveReport();

    console.log(chalk.blue(`📊 迁移摘要:`));
    console.log(chalk.green(`   ✅ 成功: ${summary.successCount} 个文件`));
    console.log(chalk.red(`   ❌ 失败: ${summary.failureCount} 个文件`));
    console.log(chalk.yellow(`   ⏭️  跳过: ${summary.skippedCount} 个文件`));
    console.log(chalk.blue(`📄 详细报告已保存: ${filePath}`));

    return {
      summary,
      reportPath: filePath
    };
  }

  /**
   * 生成失败报告 (Markdown格式)
   */
  async generateFailureReport() {
    if (this.failures.length === 0) {
      return null;
    }

    const reportData = {
      type: 'migration-failure-report',
      summary: this._generateSummary(),
      failures: this.failures,
      successes: this.successes,
      skipped: this.skipped,
      timestamp: new Date().toISOString(),
      projectPath: this.projectPath
    };

    // 使用Markdown格式保存失败报告
    const fileName = `migration-failure-report-${new Date().toISOString().split('T')[0]}.md`;
    await this.logService.logManager.writeLogFile(fileName, reportData, 'markdown');

    return this.logService.logManager.getLogFilePath(fileName);
  }

  /**
   * 结束记录会话
   */
  async finalize() {
    const summary = this._generateSummary();

    // 结束会话
    if (this.session) {
      await this.logService.endSession({
        success: summary.failureCount === 0,
        summary
      });
    }

    return summary;
  }

  /**
   * 生成摘要信息
   * @private
   */
  _generateSummary() {
    return {
      successCount: this.successes.length,
      failureCount: this.failures.length,
      skippedCount: this.skipped.length,
      totalCount: this.successes.length + this.failures.length + this.skipped.length,
      successRate: this.successes.length + this.failures.length > 0
        ? (this.successes.length / (this.successes.length + this.failures.length) * 100).toFixed(2)
        : 0
    };
  }

  /**
   * 提取错误信息
   * @private
   */
  _extractErrorMessage(error) {
    if (typeof error === 'string') return error;
    if (error && error.message) return error.message;
    if (error && typeof error === 'object') return JSON.stringify(error);
    return String(error);
  }

  /**
   * 获取文件大小
   * @private
   */
  async _getFileSize(filePath) {
    try {
      const fs = require('fs-extra');
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 获取失败统计
   */
  getFailureStats() {
    return {
      total: this.failures.length,
      byFileType: this._groupByFileType(this.failures),
      byErrorType: this._groupByErrorType(this.failures)
    };
  }

  /**
   * 按文件类型分组
   * @private
   */
  _groupByFileType(items) {
    return items.reduce((acc, item) => {
      const fileType = item.context.fileType || 'unknown';
      acc[fileType] = (acc[fileType] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * 按错误类型分组
   * @private
   */
  _groupByErrorType(failures) {
    return failures.reduce((acc, failure) => {
      // 简化错误类型分类
      const errorType = this._categorizeError(failure.error);
      acc[errorType] = (acc[errorType] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * 分类错误类型
   * @private
   */
  _categorizeError(errorMessage) {
    if (errorMessage.includes('syntax')) return 'syntax-error';
    if (errorMessage.includes('transform')) return 'transform-error';
    if (errorMessage.includes('parse')) return 'parse-error';
    if (errorMessage.includes('file')) return 'file-error';
    return 'unknown-error';
  }
}

module.exports = UnifiedFailureLogger;
