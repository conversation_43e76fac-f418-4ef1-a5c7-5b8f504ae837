  const yaml = require('js-yaml');
const chalk = require('chalk');

/**
 * 日志格式化器 - 支持多种格式的日志输出
 * 特别针对AI提示词的可读性进行优化
 */
class LogFormatter {
  constructor(options = {}) {
    this.options = {
      indentSize: 2,
      maxLineLength: 120,
      includeTimestamp: true,
      includeMetadata: true,
      ...options
    };
  }

  /**
   * 格式化日志数据为指定格式
   * @param {Object} logData - 日志数据
   * @param {string} format - 格式类型 ('json', 'yaml', 'markdown')
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的字符串
   */
  format(logData, format = 'json', options = {}) {
    const mergedOptions = { ...this.options, ...options };
    
    switch (format.toLowerCase()) {
      case 'yaml':
      case 'yml':
        return this.formatAsYAML(logData, mergedOptions);
      case 'markdown':
      case 'md':
        return this.formatAsMarkdown(logData, mergedOptions);
      case 'json':
      default:
        return this.formatAsJSON(logData, mergedOptions);
    }
  }

  /**
   * 格式化为JSON
   */
  formatAsJSON(logData, options) {
    const enrichedData = this.enrichLogData(logData, options);
    return JSON.stringify(enrichedData, null, options.indentSize);
  }

  /**
   * 格式化为YAML - 特别适合AI提示词
   */
  formatAsYAML(logData, options) {
    const enrichedData = this.enrichLogData(logData, options);
    
    // 对AI提示词进行特殊处理
    if (enrichedData.type === 'ai-prompt' || enrichedData.prompt) {
      return this.formatAIPromptAsYAML(enrichedData, options);
    }
    
    return yaml.dump(enrichedData, {
      indent: options.indentSize,
      lineWidth: options.maxLineLength,
      noRefs: true,
      sortKeys: false
    });
  }

  /**
   * 格式化AI提示词为YAML
   */
  formatAIPromptAsYAML(logData, options) {
    const { prompt, response, metadata, ...rest } = logData;
    
    let yamlContent = '';
    
    // 基础信息
    if (options.includeMetadata && metadata) {
      yamlContent += '# AI调用日志\n';
      yamlContent += `timestamp: ${metadata.timestamp || new Date().toISOString()}\n`;
      yamlContent += `session_id: ${metadata.sessionId || 'unknown'}\n`;
      yamlContent += `attempt: ${metadata.attemptNumber || 1}\n`;
      yamlContent += `phase: ${metadata.phase || 'unknown'}\n\n`;
    }
    
    // 提示词部分
    if (prompt) {
      yamlContent += 'prompt: |\n';
      const promptLines = this.formatMultilineText(prompt, options.indentSize);
      yamlContent += promptLines + '\n\n';
    }
    
    // 响应部分
    if (response) {
      yamlContent += 'response: |\n';
      const responseLines = this.formatMultilineText(response, options.indentSize);
      yamlContent += responseLines + '\n\n';
    }
    
    // 其他数据
    const otherData = { ...rest };
    delete otherData.timestamp;
    
    if (Object.keys(otherData).length > 0) {
      yamlContent += yaml.dump(otherData, {
        indent: options.indentSize,
        lineWidth: options.maxLineLength,
        noRefs: true
      });
    }
    
    return yamlContent;
  }

  /**
   * 格式化为Markdown - 适合报告类日志
   */
  formatAsMarkdown(logData, options) {
    const enrichedData = this.enrichLogData(logData, options);
    
    // 根据日志类型选择不同的Markdown格式
    switch (enrichedData.type) {
      case 'validation-report':
        return this.formatValidationReportAsMarkdown(enrichedData, options);
      case 'migration-error':
        return this.formatMigrationErrorAsMarkdown(enrichedData, options);
      case 'session-summary':
        return this.formatSessionSummaryAsMarkdown(enrichedData, options);
      default:
        return this.formatGenericAsMarkdown(enrichedData, options);
    }
  }

  /**
   * 格式化验证报告为Markdown
   */
  formatValidationReportAsMarkdown(logData, options) {
    let markdown = `# 验证报告\n\n`;
    
    if (options.includeTimestamp) {
      markdown += `**生成时间**: ${logData.timestamp}\n\n`;
    }
    
    // 摘要
    if (logData.summary) {
      markdown += `## 摘要\n\n`;
      markdown += `- 总页面数: ${logData.summary.total}\n`;
      markdown += `- 成功: ${logData.summary.successful}\n`;
      markdown += `- 失败: ${logData.summary.failed}\n`;
      markdown += `- 成功率: ${logData.summary.successRate}%\n\n`;
    }
    
    // 失败详情
    if (logData.failedPages && logData.failedPages.length > 0) {
      markdown += `## 失败页面详情\n\n`;
      logData.failedPages.forEach((page, index) => {
        markdown += `### ${index + 1}. ${page.url || page.path}\n\n`;
        if (page.errors && page.errors.length > 0) {
          markdown += `**错误信息**:\n`;
          page.errors.forEach(error => {
            markdown += `- ${error}\n`;
          });
          markdown += '\n';
        }
      });
    }
    
    return markdown;
  }

  /**
   * 格式化迁移错误为Markdown
   */
  formatMigrationErrorAsMarkdown(logData, options) {
    let markdown = `# 迁移错误报告\n\n`;
    
    if (options.includeTimestamp) {
      markdown += `**时间**: ${logData.timestamp}\n`;
    }
    
    markdown += `**文件**: ${logData.file}\n`;
    markdown += `**错误**: ${logData.error}\n\n`;
    
    if (logData.context) {
      markdown += `## 上下文信息\n\n`;
      markdown += '```json\n';
      markdown += JSON.stringify(logData.context, null, 2);
      markdown += '\n```\n\n';
    }
    
    if (logData.stack) {
      markdown += `## 错误堆栈\n\n`;
      markdown += '```\n';
      markdown += logData.stack;
      markdown += '\n```\n';
    }
    
    return markdown;
  }

  /**
   * 格式化会话摘要为Markdown
   */
  formatSessionSummaryAsMarkdown(logData, options) {
    let markdown = `# 会话摘要\n\n`;
    
    markdown += `**会话ID**: ${logData.sessionId}\n`;
    markdown += `**开始时间**: ${logData.startTime}\n`;
    markdown += `**结束时间**: ${logData.endTime || '进行中'}\n\n`;
    
    if (logData.statistics) {
      markdown += `## 统计信息\n\n`;
      const stats = logData.statistics;
      markdown += `- 总尝试次数: ${stats.totalAttempts}\n`;
      markdown += `- 分析文件数: ${stats.filesAnalyzed}\n`;
      markdown += `- 修改文件数: ${stats.filesModified}\n`;
      markdown += `- 修复错误数: ${stats.errorsFixed}\n`;
      markdown += `- AI调用次数: ${stats.aiCalls}\n`;
      markdown += `- 总耗时: ${stats.totalDuration}ms\n\n`;
    }
    
    return markdown;
  }

  /**
   * 通用Markdown格式化
   */
  formatGenericAsMarkdown(logData, options) {
    let markdown = `# 日志记录\n\n`;
    
    if (options.includeTimestamp) {
      markdown += `**时间**: ${logData.timestamp}\n\n`;
    }
    
    // 将对象转换为Markdown表格或列表
    Object.entries(logData).forEach(([key, value]) => {
      if (key === 'timestamp') return;
      
      markdown += `## ${this.capitalizeFirst(key)}\n\n`;
      
      if (typeof value === 'object') {
        markdown += '```json\n';
        markdown += JSON.stringify(value, null, 2);
        markdown += '\n```\n\n';
      } else {
        markdown += `${value}\n\n`;
      }
    });
    
    return markdown;
  }

  /**
   * 格式化多行文本
   */
  formatMultilineText(text, indentSize = 2) {
    const indent = ' '.repeat(indentSize);
    return text.split('\n').map(line => indent + line).join('\n');
  }

  /**
   * 丰富日志数据
   */
  enrichLogData(logData, options) {
    const enriched = { ...logData };
    
    if (options.includeTimestamp && !enriched.timestamp) {
      enriched.timestamp = new Date().toISOString();
    }
    
    if (options.includeMetadata && !enriched.metadata) {
      enriched.metadata = {
        formatter: 'LogFormatter',
        version: '1.0.0'
      };
    }
    
    return enriched;
  }

  /**
   * 首字母大写
   */
  capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * 获取文件扩展名
   */
  getFileExtension(format) {
    switch (format.toLowerCase()) {
      case 'yaml':
      case 'yml':
        return '.yml';
      case 'markdown':
      case 'md':
        return '.md';
      case 'json':
      default:
        return '.json';
    }
  }
}

module.exports = LogFormatter;
